# Kamailio SIP Server with PostgreSQL Installation Guide

## System Information
- **Target Server**: km1.ethiopiatrips.com (*************)
- **Operating System**: Debian 12 (bookworm)
- **User Context**: admin user with sudo privileges
- **Connection Method**: SSH key authentication
- **Installation Date**: June 1, 2025

## Table of Contents
1. [Prerequisites and System Preparation](#prerequisites-and-system-preparation)
2. [PostgreSQL Database Installation](#postgresql-database-installation)
3. [Kamailio SIP Server Installation](#kamailio-sip-server-installation)
4. [Service Integration and Configuration](#service-integration-and-configuration)
5. [Verification and Testing](#verification-and-testing)
6. [Security Considerations](#security-considerations)
7. [Troubleshooting](#troubleshooting)

---

## 1. Prerequisites and System Preparation

### 1.1 System Update and Verification

First, verify the system information and update the package repositories:

```bash
# Verify system information
cat /etc/os-release
df -h

# Update package repositories
sudo apt update
sudo apt upgrade -y
```

**Expected Output:**
```
PRETTY_NAME="Debian GNU/Linux 12 (bookworm)"
VERSION_ID="12"
```

### 1.2 Install Required Dependencies

Install essential packages for the installation process:

```bash
sudo apt install -y wget curl gnupg2 software-properties-common apt-transport-https ca-certificates
```

**Purpose**: These packages provide secure package management, GPG verification, and HTTPS transport capabilities.

### 1.3 Network and Firewall Considerations

**Important**: Ensure the following ports are available:
- **5060/UDP**: SIP signaling (default)
- **5432/TCP**: PostgreSQL database (if remote access needed)
- **22/TCP**: SSH access (already configured)

---

## 2. PostgreSQL Database Installation

### 2.1 Install PostgreSQL 15

Install PostgreSQL server and client packages:

```bash
sudo apt install -y postgresql postgresql-contrib postgresql-client
```

**Package Details:**
- `postgresql`: Main PostgreSQL server
- `postgresql-contrib`: Additional contributed modules
- `postgresql-client`: Command-line client tools

### 2.2 Configure PostgreSQL Service

Start and enable PostgreSQL service:

```bash
# Start PostgreSQL service
sudo systemctl start postgresql

# Enable auto-start on boot
sudo systemctl enable postgresql

# Verify service status
sudo systemctl status postgresql
```

**Expected Output:**
```
● postgresql.service - PostgreSQL RDBMS
     Loaded: loaded (/lib/systemd/system/postgresql.service; enabled; preset: enabled)
     Active: active (exited) since Sun 2025-06-01 11:22:02 UTC
```

### 2.3 Create Kamailio Database and User

Create dedicated database and user for Kamailio:

```bash
# Create kamailio user
sudo -u postgres createuser kamailio

# Create kamailio database
sudo -u postgres createdb kamailio

# Set password for kamailio user
sudo -u postgres psql -c "ALTER USER kamailio WITH PASSWORD 'kamailio123';"

# Grant privileges to kamailio user
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE kamailio TO kamailio;"
```

**Security Note**: Change 'kamailio123' to a strong password in production environments.

### 2.4 Verify Database Creation

Test database connectivity:

```bash
# Test connection as kamailio user
sudo -u postgres psql -c "\l" | grep kamailio
```

**Expected Output:**
```
 kamailio | kamailio | UTF8     | C.UTF-8 | C.UTF-8 |
```

---

## 3. Kamailio SIP Server Installation

### 3.1 Install Kamailio Packages

Install Kamailio core and required modules:

```bash
sudo apt install -y kamailio kamailio-postgres-modules kamailio-utils-modules kamailio-extra-modules
```

**Package Details:**
- `kamailio`: Core SIP server
- `kamailio-postgres-modules`: PostgreSQL database integration
- `kamailio-utils-modules`: Utility modules for enhanced functionality
- `kamailio-extra-modules`: Additional protocol and feature modules

### 3.2 Configure Database Connection

Edit the Kamailio control configuration file:

```bash
# Backup original configuration
sudo cp /etc/kamailio/kamctlrc /etc/kamailio/kamctlrc.backup

# Configure database engine
sudo sed -i 's/# DBENGINE=MYSQL/DBENGINE=PGSQL/' /etc/kamailio/kamctlrc

# Configure database host
sudo sed -i 's/# DBHOST=localhost/DBHOST=localhost/' /etc/kamailio/kamctlrc

# Configure database name
sudo sed -i 's/# DBNAME=kamailio/DBNAME=kamailio/' /etc/kamailio/kamctlrc

# Configure database user
sudo sed -i 's/# DBRWUSER="kamailio"/DBRWUSER="kamailio"/' /etc/kamailio/kamctlrc

# Configure database password
sudo sed -i 's/# DBRWPW="kamailiorw"/DBRWPW="kamailio123"/' /etc/kamailio/kamctlrc
```

### 3.3 Verify Configuration

Check the updated configuration:

```bash
grep -E "^(DBENGINE|DBHOST|DBNAME|DBRWUSER|DBRWPW)" /etc/kamailio/kamctlrc
```

**Expected Output:**
```
DBENGINE=PGSQL
DBHOST=localhost
DBNAME=kamailio
DBRWUSER="kamailio"
DBRWPW="kamailio123"
```

### 3.4 Create Database Schema

Initialize the Kamailio database schema:

```bash
# Create database tables
sudo kamdbctl create
```

**Interactive Prompts**: The command will ask for confirmation to create tables. Answer 'y' to proceed.

---

## 4. Service Integration and Configuration

### 4.1 Configure Kamailio Service

The Kamailio service is automatically configured during installation. Verify the configuration:

```bash
# Check service status
sudo systemctl status kamailio

# Enable auto-start (should already be enabled)
sudo systemctl enable kamailio
```

### 4.2 Key Configuration Files

**Important Configuration Files:**
- `/etc/kamailio/kamailio.cfg`: Main Kamailio configuration
- `/etc/kamailio/kamctlrc`: Database and control tool configuration
- `/lib/systemd/system/kamailio.service`: Systemd service definition

### 4.3 Service Dependencies

Kamailio depends on PostgreSQL. Ensure proper startup order:

```bash
# Verify PostgreSQL is running before starting Kamailio
sudo systemctl is-active postgresql
sudo systemctl start kamailio
```

---

## 5. Verification and Testing

### 5.1 Service Status Verification

Check all services are running:

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check Kamailio status
sudo systemctl status kamailio

# Check listening ports
sudo netstat -tlnp | grep -E "(5060|5432)"
```

### 5.2 Database Connectivity Test

Verify Kamailio can connect to PostgreSQL:

```bash
# Test database connection
sudo kamctl db show
```

### 5.3 Basic Functionality Test

Test basic Kamailio functionality:

```bash
# Check Kamailio version
kamailio -v

# Validate configuration syntax
sudo kamailio -c -f /etc/kamailio/kamailio.cfg
```

---

## 6. Security Considerations

### 6.1 Database Security

**Immediate Actions:**
1. Change default passwords from 'kamailio123' to strong passwords
2. Restrict PostgreSQL access to localhost only (default configuration)
3. Enable PostgreSQL logging for security monitoring

### 6.2 Firewall Configuration

Configure firewall rules for SIP traffic:

```bash
# Allow SIP traffic (if using ufw)
sudo ufw allow 5060/udp comment "Kamailio SIP"
sudo ufw allow 5060/tcp comment "Kamailio SIP"

# Restrict PostgreSQL access (local only)
sudo ufw deny 5432/tcp comment "PostgreSQL - local only"
```

### 6.3 Production Recommendations

1. **SSL/TLS Configuration**: Configure TLS for SIP over secure transport
2. **Authentication**: Implement proper SIP authentication mechanisms
3. **Monitoring**: Set up log monitoring and alerting
4. **Backup**: Implement regular database backup procedures
5. **Updates**: Establish regular security update procedures

---

## 7. Troubleshooting

### 7.1 Common Issues

**Issue**: Kamailio fails to start
**Solution**: Check configuration syntax and database connectivity

```bash
sudo kamailio -c -f /etc/kamailio/kamailio.cfg
sudo kamctl db show
```

**Issue**: Database connection errors
**Solution**: Verify PostgreSQL service and credentials

```bash
sudo systemctl status postgresql
sudo -u postgres psql -c "\du" | grep kamailio
```

**Issue**: Permission denied errors
**Solution**: Check file permissions and user privileges

```bash
ls -la /etc/kamailio/
sudo chown -R kamailio:kamailio /var/run/kamailio/
```

### 7.2 Log Files

**Important Log Locations:**
- Kamailio logs: `/var/log/syslog` (default)
- PostgreSQL logs: `/var/log/postgresql/`
- System logs: `journalctl -u kamailio` and `journalctl -u postgresql`

### 7.3 Diagnostic Commands

```bash
# Check Kamailio process
ps aux | grep kamailio

# Check database connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE datname='kamailio';"

# Test SIP functionality
kamctl ul show
```

---

## Installation Summary

This installation provides:
- ✅ PostgreSQL 15 database server
- ✅ Kamailio 5.6.3 SIP server
- ✅ Database integration and schema
- ✅ Systemd service configuration
- ✅ Basic security configuration

**Next Steps:**
1. Configure SIP routing logic in `/etc/kamailio/kamailio.cfg`
2. Set up SIP user accounts and authentication
3. Configure NAT traversal if needed
4. Implement monitoring and backup procedures
5. Perform security hardening for production use

**Support Information:**
- Kamailio Documentation: https://www.kamailio.org/docs/
- PostgreSQL Documentation: https://www.postgresql.org/docs/
- Debian Package Information: https://packages.debian.org/
